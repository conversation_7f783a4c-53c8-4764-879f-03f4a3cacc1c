[package]
name = "youtube_downloader"
version = "0.1.0"
edition = "2021"

[dependencies]
# GUI Framework
eframe = "0.32.3"
egui = "0.32.3"
dirs = "4.0"

# System file dialogs
rfd = "0.15.4"

# Date/time handling
chrono = "0.4.42"

# JSON serialization/deserialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# HTTP server for Web UI (Hyper 1.x)
hyper = { version = "1.7.0", features = ["full"] }
tokio = { version = "1", features = ["full"] }

# For URL parsing and encoding
url = "2.5"

# Optional: if you want to use regex for advanced URL validation
regex = "1.10"

# Optional: progress bars or logging (if you plan to enhance CLI output later)
indicatif = "0.17"

# Optional: command-line argument parsing (not used in current GUI app)
# clap = { version = "4.4", features = ["derive"] }

# Optional: URL encoding utilities (already covered by `url` crate)
# urlencoding = "2.1.3"

# Optional: async utilities (covered by tokio)
# futures-util = "0.3"

# Optional: HTTP client (not used — we're using Hyper as server, not client)
# reqwest = { version = "0.11", features = ["json", "stream"] }

[profile.release]
lto = true
opt-level = "z"


 