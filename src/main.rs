use eframe::{egui, NativeOptions};
use std::process::{Command, Stdio};
use std::thread;
use std::sync::{<PERSON>, Mutex};
use std::io::{<PERSON>ufR<PERSON>, BufReader};
use std::time::Duration;
use std::collections::{VecDeque, HashMap};
use chrono::Local;
use serde::{Deserialize, Serialize};
use rfd::FileDialog;
use dirs::download_dir;
use hyper::body::{Incoming, Bytes, to_bytes};
use hyper::server::Server;
use hyper::{Request, Response, service::service_fn, StatusCode, Method};
use hyper::header::CONTENT_TYPE;
use tokio::runtime::Runtime;

#[derive(Serialize, Deserialize, Clone)]
struct DownloadHistory {
    url: String,
    title: String,
    timestamp: String,
    status: String,
    file_path: String,
}

#[derive(<PERSON>lone)]
struct QueueItem {
    url: String,
    format: String,
    priority: u8,
    id: usize,
}

#[derive(Clone, PartialEq)]
enum Theme {
    Light,
    Dark,
}

#[derive(Clone, Debug)]
struct ChunkInfo {
    id: usize,
    start_byte: u64,
    end_byte: u64,
    progress: f32,
    status: String,
    retry_count: u32,
    download_speed: String,
}

#[derive(Clone)]
struct ChunkSettings {
    enabled: bool,
    num_chunks: usize,
    chunk_size_mb: f32,
    max_parallel_chunks: usize,
    auto_merge: bool,
    verify_chunks: bool,
    retry_failed_chunks: bool,
    max_chunk_retries: u32,
}

impl Default for ChunkSettings {
    fn default() -> Self {
        Self {
            enabled: false,
            num_chunks: 4,
            chunk_size_mb: 10.0,
            max_parallel_chunks: 4,
            auto_merge: true,
            verify_chunks: true,
            retry_failed_chunks: true,
            max_chunk_retries: 3,
        }
    }
}

#[derive(Clone, Default)]
struct VideoMetadata {
    title: String,
    duration: String,
    uploader: String,
    view_count: String,
    description: String,
    upload_date: String,
    file_size: String,
    resolution: String,
}

// Web UI server state
#[derive(Clone)]
struct WebUiState {
    enabled: bool,
    port: u16,
    running: Arc<Mutex<bool>>,
    shutdown_tx: Option<std::sync::mpsc::Sender<()>>,
}

impl Default for WebUiState {
    fn default() -> Self {
        Self {
            enabled: false,
            port: 8080,
            running: Arc::new(Mutex::new(false)),
            shutdown_tx: None,
        }
    }
}

pub struct YouTubeDownloader {
    // Basic fields
    url: String,
    format: String,
    output_path: String,
    status: Arc<Mutex<String>>,
    is_downloading: Arc<Mutex<bool>>,
    download_process: Arc<Mutex<Option<std::process::Child>>>,
    cancel_download: Arc<Mutex<bool>>,

    // UI state
    current_tab: usize,
    show_advanced: bool,
    
    // Format options
    formats: Vec<String>,
    selected_format: usize,
    quality_options: Vec<String>,
    selected_quality: usize,
    
    // Basic options
    download_subtitles: bool,
    extract_thumbnail: bool,
    playlist_mode: bool,
    resume_downloads: bool,
    audio_only: bool,
    audio_format: String,
    
    // Progress tracking
    download_progress: Arc<Mutex<f32>>,
    download_speed: Arc<Mutex<String>>,
    eta: Arc<Mutex<String>>,
    
    // Advanced features
    video_metadata: Arc<Mutex<VideoMetadata>>,
    download_history: Vec<DownloadHistory>,
    show_history: bool,
    download_queue: VecDeque<QueueItem>,
    process_queue: bool,
    
    // Network options
    speed_limit: String,
    enable_speed_limit: bool,
    proxy_url: String,
    use_proxy: bool,
    max_retries: u32,
    retry_count: Arc<Mutex<u32>>,
    
    // Authentication
    username: String,
    password: String,
    use_auth: bool,
    cookies_file: String,
    use_cookies: bool,
    
    // Filename and metadata
    filename_template: String,
    use_custom_filename: bool,
    embed_metadata: bool,
    embed_thumbnail: bool,
    download_description: bool,
    
    // Video editing
    start_time: String,
    end_time: String,
    enable_trimming: bool,
    
    // Chunk downloading
    chunk_settings: ChunkSettings,
    active_chunks: Arc<Mutex<HashMap<usize, ChunkInfo>>>,
    chunk_progress: Arc<Mutex<Vec<f32>>>,
    total_file_size: Arc<Mutex<u64>>,
    downloaded_bytes: Arc<Mutex<u64>>,
    show_chunk_details: bool,
    
    // Theme and UI
    theme: Theme,
    
    // Statistics
    total_downloads: usize,
    failed_downloads: usize,
    total_size_downloaded: f64,
    
    // Folder selection
    selected_folder: Option<String>,
    
    // Web UI
    web_ui: WebUiState,
}

impl Default for YouTubeDownloader {
    fn default() -> Self {
        let output_path = match download_dir() {
            Some(path) => path.to_string_lossy().to_string(),
            None => "./downloads/".to_string(),
        };
        
        Self {
            url: String::new(),
            format: String::from("best"),
            output_path,
            status: Arc::new(Mutex::new("Ready".to_string())),
            is_downloading: Arc::new(Mutex::new(false)),
            download_process: Arc::new(Mutex::new(None)),
            cancel_download: Arc::new(Mutex::new(false)),
            
            current_tab: 0,
            show_advanced: false,
            
            formats: vec![
                "best".to_string(),
                "bestvideo+bestaudio".to_string(),
                "mp4".to_string(),
                "webm".to_string(),
            ],
            selected_format: 0,
            
            quality_options: vec![
                "best".to_string(),
                "1080p".to_string(),
                "720p".to_string(),
                "480p".to_string(),
                "360p".to_string(),
            ],
            selected_quality: 0,
            
            download_subtitles: false,
            extract_thumbnail: false,
            playlist_mode: false,
            resume_downloads: true,
            audio_only: false,
            audio_format: "mp3".to_string(),
            
            download_progress: Arc::new(Mutex::new(0.0)),
            download_speed: Arc::new(Mutex::new("0 MB/s".to_string())),
            eta: Arc::new(Mutex::new("--:--".to_string())),
            
            video_metadata: Arc::new(Mutex::new(VideoMetadata::default())),
            download_history: Vec::new(),
            show_history: false,
            download_queue: VecDeque::new(),
            process_queue: false,
            
            speed_limit: "1M".to_string(),
            enable_speed_limit: false,
            proxy_url: String::new(),
            use_proxy: false,
            max_retries: 3,
            retry_count: Arc::new(Mutex::new(0)),
            
            username: String::new(),
            password: String::new(),
            use_auth: false,
            cookies_file: String::new(),
            use_cookies: false,
            
            filename_template: "%(title)s-%(id)s.%(ext)s".to_string(),
            use_custom_filename: false,
            embed_metadata: true,
            embed_thumbnail: false,
            download_description: false,
            
            start_time: String::new(),
            end_time: String::new(),
            enable_trimming: false,
            
            chunk_settings: ChunkSettings::default(),
            active_chunks: Arc::new(Mutex::new(HashMap::new())),
            chunk_progress: Arc::new(Mutex::new(Vec::new())),
            total_file_size: Arc::new(Mutex::new(0)),
            downloaded_bytes: Arc::new(Mutex::new(0)),
            show_chunk_details: false,
            
            theme: Theme::Dark,
            
            total_downloads: 0,
            failed_downloads: 0,
            total_size_downloaded: 0.0,
            
            selected_folder: None,
            
            web_ui: WebUiState::default(),
        }
    }
}

impl YouTubeDownloader {
    fn build_command(&self) -> Vec<String> {
        let mut args = vec![self.url.clone()];
        
        // Format selection
        if self.audio_only {
            args.extend_from_slice(&["--extract-audio".to_string(), 
                                   "--audio-format".to_string(), 
                                   self.audio_format.clone()]);
        } else {
            args.push("-f".to_string());
            if self.selected_quality > 0 {
                let quality = self.quality_options[self.selected_quality].replace("p", "");
                args.push(format!("bestvideo[height<={}]+bestaudio/best[height<={}]", quality, quality));
            } else {
                args.push(self.formats[self.selected_format].clone());
            }
        }
        
        // Output path
        let output_template = if self.use_custom_filename { 
            &self.filename_template 
        } else { 
            "%(title)s.%(ext)s" 
        };
        
        args.extend_from_slice(&["-o".to_string(), 
                               format!("{}{}", self.output_path, output_template)]);
        
        // Chunk downloading
        if self.chunk_settings.enabled {
            args.extend_from_slice(&["--concurrent-fragments".to_string(),
                                   self.chunk_settings.max_parallel_chunks.to_string()]);
        }
        
        // Basic options
        if self.download_subtitles {
            args.push("--write-sub".to_string());
        }
        if self.extract_thumbnail {
            args.push("--write-thumbnail".to_string());
        }
        if self.embed_thumbnail {
            args.push("--embed-thumbnail".to_string());
        }
        if self.playlist_mode {
            args.push("--yes-playlist".to_string());
        } else {
            args.push("--no-playlist".to_string());
        }
        if self.resume_downloads {
            args.push("-c".to_string());
        }
        if self.embed_metadata {
            args.push("--embed-metadata".to_string());
        }
        if self.download_description {
            args.push("--write-description".to_string());
        }
        
        // Network options
        if self.enable_speed_limit && !self.speed_limit.is_empty() {
            args.extend_from_slice(&["-r".to_string(), self.speed_limit.clone()]);
        }
        if self.use_proxy && !self.proxy_url.is_empty() {
            args.extend_from_slice(&["--proxy".to_string(), self.proxy_url.clone()]);
        }
        if self.use_auth && !self.username.is_empty() {
            args.extend_from_slice(&["-u".to_string(), self.username.clone(),
                                   "-p".to_string(), self.password.clone()]);
        }
        if self.use_cookies && !self.cookies_file.is_empty() {
            args.extend_from_slice(&["--cookies".to_string(), self.cookies_file.clone()]);
        }
        
        // Video trimming
        if self.enable_trimming && !self.start_time.is_empty() {
            args.extend_from_slice(&["--download-sections".to_string(),
                                   format!("*{}-{}", self.start_time, 
                                          if self.end_time.is_empty() { "inf".to_string() } 
                                          else { self.end_time.clone() })]);
        }
        
        args.extend_from_slice(&["--newline".to_string(), "--progress".to_string()]);
        
        args
    }
    
    fn download(&mut self) {
        if *self.is_downloading.lock().unwrap() {
            return;
        }
        
        let status = self.status.clone();
        let is_downloading = self.is_downloading.clone();
        let progress = self.download_progress.clone();
        let speed = self.download_speed.clone();
        let eta = self.eta.clone();
        let retry_count = self.retry_count.clone();
        let max_retries = self.max_retries;
        let cancel_download = self.cancel_download.clone();
        let download_process = self.download_process.clone();
        let args = self.build_command();
        
        *is_downloading.lock().unwrap() = true;
        *status.lock().unwrap() = "Starting download...".to_string();
        *progress.lock().unwrap() = 0.0;
        *retry_count.lock().unwrap() = 0;
        *cancel_download.lock().unwrap() = false;
        
        thread::spawn(move || {
            let mut attempt = 0;
            let mut child = None;
            
            loop {
                if *cancel_download.lock().unwrap() {
                    if let Some(mut proc) = child.take() {
                        let _ = proc.kill();
                        *status.lock().unwrap() = "Download cancelled by user".to_string();
                        *is_downloading.lock().unwrap() = false;
                        return;
                    }
                }
                
                let cmd_result = Command::new("yt-dlp")
                    .args(&args)
                    .stdout(Stdio::piped())
                    .stderr(Stdio::piped())
                    .spawn();
                
                match cmd_result {
                    Ok(mut proc) => {
                        child = Some(proc);
                        *download_process.lock().unwrap() = child.clone();
                        
                        if let Some(stdout) = child.as_mut().unwrap().stdout.take() {
                            let reader = BufReader::new(stdout);
                            for line in reader.lines() {
                                if *cancel_download.lock().unwrap() {
                                    break;
                                }
                                
                                if let Ok(line) = line {
                                    Self::parse_progress_line(&line, &progress, &speed, &eta);
                                    *status.lock().unwrap() = line.clone();
                                }
                            }
                        }
                        
                        let output = child.take().unwrap().wait_with_output();
                        
                        match output {
                            Ok(output) => {
                                if output.status.success() {
                                    *status.lock().unwrap() = "Download completed successfully!".to_string();
                                    *progress.lock().unwrap() = 100.0;
                                    break;
                                } else {
                                    attempt += 1;
                                    *retry_count.lock().unwrap() = attempt;
                                    
                                    if attempt >= max_retries {
                                        let error = String::from_utf8_lossy(&output.stderr);
                                        *status.lock().unwrap() = format!("Error after {} retries: {}", max_retries, error);
                                        break;
                                    } else {
                                        *status.lock().unwrap() = format!("Retrying... (Attempt {}/{})", attempt, max_retries);
                                        thread::sleep(Duration::from_secs(2));
                                    }
                                }
                            }
                            Err(e) => {
                                *status.lock().unwrap() = format!("Failed to execute command: {}", e);
                                break;
                            }
                        }
                    }
                    Err(e) => {
                        *status.lock().unwrap() = format!("Failed to start yt-dlp: {}", e);
                        *is_downloading.lock().unwrap() = false;
                        return;
                    }
                }
            }
            
            *is_downloading.lock().unwrap() = false;
            *download_process.lock().unwrap() = None;
        });
        
        self.add_to_history("Started".to_string());
        self.total_downloads += 1;
    }
    
    fn parse_progress_line(
        line: &str, 
        progress: &Arc<Mutex<f32>>, 
        speed: &Arc<Mutex<String>>, 
        eta: &Arc<Mutex<String>>
    ) {
        if line.contains("%") {
            if let Some(percent_pos) = line.find('%') {
                let start = line[..percent_pos].rfind(' ').unwrap_or(0);
                if let Ok(percent) = line[start+1..percent_pos].trim().parse::<f32>() {
                    *progress.lock().unwrap() = percent;
                }
            }
        }
        
        if line.contains("iB/s") {
            if let Some(speed_pos) = line.find("iB/s") {
                let start = line[..speed_pos].rfind(' ').unwrap_or(0);
                let speed_str = &line[start+1..speed_pos+4];
                *speed.lock().unwrap() = speed_str.to_string();
            }
        }
        
        if line.contains("ETA") {
            if let Some(eta_pos) = line.find("ETA") {
                let eta_str = line[eta_pos+4..].trim();
                *eta.lock().unwrap() = eta_str.to_string();
            }
        }
    }
    
    fn add_to_history(&mut self, status: String) {
        self.download_history.push(DownloadHistory {
            url: self.url.clone(),
            title: "".to_string(),
            timestamp: Local::now().format("%Y-%m-%d %H:%M:%S").to_string(),
            status,
            file_path: self.output_path.clone(),
        });
    }
    
    fn add_to_queue(&mut self) {
        if !self.url.is_empty() {
            let id = self.download_queue.len() + 1;
            self.download_queue.push_back(QueueItem {
                url: self.url.clone(),
                format: self.formats[self.selected_format].clone(),
                priority: 5,
                id,
            });
            *self.status.lock().unwrap() = "Added to queue".to_string();
        }
    }
    
    fn process_download_queue(&mut self) {
        if !self.process_queue || self.download_queue.is_empty() || *self.is_downloading.lock().unwrap() {
            return;
        }
        
        if let Some(item) = self.download_queue.pop_front() {
            self.url = item.url;
            self.format = item.format;
            self.download();
        }
    }
    
    fn apply_theme(&self, ctx: &egui::Context) {
        let visuals = match self.theme {
            Theme::Dark => {
                let mut visuals = egui::Visuals::dark();
                visuals.window_fill = egui::Color32::from_rgb(30, 30, 30);
                visuals.panel_fill = egui::Color32::from_rgb(35, 35, 35);
                visuals
            },
            Theme::Light => egui::Visuals::light(),
        };
        ctx.set_visuals(visuals);
    }
    
    fn draw_header(&mut self, ui: &mut egui::Ui) {
        ui.horizontal(|ui| {
            ui.heading("YouTube Downloader");
            
            ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                if ui.small_button(match self.theme {
                    Theme::Dark => "🌞 Light",
                    Theme::Light => "🌙 Dark",
                }).clicked() {
                    self.theme = match self.theme {
                        Theme::Dark => Theme::Light,
                        Theme::Light => Theme::Dark,
                    };
                }
                
                ui.separator();
                
                ui.label(format!("Downloads: {} | Failed: {}", 
                    self.total_downloads, self.failed_downloads));
            });
        });
    }
    
    fn draw_main_controls(&mut self, ui: &mut egui::Ui) {
        ui.group(|ui| {
            ui.vertical(|ui| {
                ui.label("Video URL:");
                ui.horizontal(|ui| {
                    let response = ui.add(
                        egui::TextEdit::singleline(&mut self.url)
                            .desired_width(ui.available_width() - 120.0)
                            .hint_text("Enter YouTube URL here...")
                    );
                    
                    if response.changed() && !self.url.is_empty() {
                        // Could fetch metadata here
                    }
                    
                    if ui.button("Add to Queue").clicked() {
                        self.add_to_queue();
                    }
                });
                
                ui.horizontal(|ui| {
                    let is_downloading = *self.is_downloading.lock().unwrap();
                    
                    if ui.add_enabled(!is_downloading && !self.url.is_empty(), 
                        egui::Button::new("⬇ Download")).clicked() {
                        self.download();
                    }
                    
                    if is_downloading {
                        if ui.button("⏹ Cancel").clicked() {
                            *self.cancel_download.lock().unwrap() = true;
                            *self.status.lock().unwrap() = "Cancelling download...".to_string();
                        }
                    }
                    
                    ui.checkbox(&mut self.process_queue, "Auto Process Queue");
                    
                    ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                        if ui.button(if self.show_advanced { "Hide Advanced" } else { "Show Advanced" }).clicked() {
                            self.show_advanced = !self.show_advanced;
                        }
                    });
                });
                
                // Folder selection
                ui.horizontal(|ui| {
                    ui.label("Download Folder:");
                    if ui.button("📁 Select").clicked() {
                        if let Some(path) = FileDialog::new()
                            .set_title("Select Download Folder")
                            .pick_folder()
                        {
                            self.output_path = path.to_string_lossy().to_string();
                            self.selected_folder = Some(self.output_path.clone());
                        }
                    }
                    
                    ui.text_edit_singleline(&mut self.output_path);
                });
            });
        });
    }
    
    fn draw_progress(&self, ui: &mut egui::Ui) {
        ui.group(|ui| {
            ui.vertical(|ui| {
                ui.label("Progress:");
                
                let progress = *self.download_progress.lock().unwrap() / 100.0;
                ui.add(egui::ProgressBar::new(progress)
                    .show_percentage()
                    .desired_height(20.0));
                
                ui.horizontal(|ui| {
                    ui.label(format!("Speed: {}", *self.download_speed.lock().unwrap()));
                    ui.separator();
                    ui.label(format!("ETA: {}", *self.eta.lock().unwrap()));
                    
                    let retry_count = *self.retry_count.lock().unwrap();
                    if retry_count > 0 {
                        ui.separator();
                        ui.label(format!("Retry: {}/{}", retry_count, self.max_retries));
                    }
                });
            });
        });
    }
    
    fn draw_status(&self, ui: &mut egui::Ui) {
        ui.group(|ui| {
            ui.vertical(|ui| {
                ui.label("Status:");
                egui::ScrollArea::vertical()
                    .max_height(80.0)
                    .show(ui, |ui| {
                        ui.label(&*self.status.lock().unwrap());
                    });
            });
        });
    }
    
    fn draw_basic_options(&mut self, ui: &mut egui::Ui) {
        ui.horizontal(|ui| {
            ui.vertical(|ui| {
                ui.label("Format:");
                egui::ComboBox::from_id_salt("format")
                    .selected_text(&self.formats[self.selected_format])
                    .show_ui(ui, |ui| {
                        for (i, format) in self.formats.iter().enumerate() {
                            ui.selectable_value(&mut self.selected_format, i, format);
                        }
                    });
                
                ui.checkbox(&mut self.audio_only, "Audio Only");
                if self.audio_only {
                    ui.horizontal(|ui| {
                        ui.label("Audio Format:");
                        egui::ComboBox::from_id_salt("audio_format")
                            .selected_text(&self.audio_format)
                            .show_ui(ui, |ui| {
                                ui.selectable_value(&mut self.audio_format, "mp3".to_string(), "MP3");
                                ui.selectable_value(&mut self.audio_format, "flac".to_string(), "FLAC");
                                ui.selectable_value(&mut self.audio_format, "wav".to_string(), "WAV");
                                ui.selectable_value(&mut self.audio_format, "m4a".to_string(), "M4A");
                            });
                    });
                }
            });
            
            ui.separator();
            
            ui.vertical(|ui| {
                ui.label("Quality:");
                egui::ComboBox::from_id_salt("quality")
                    .selected_text(&self.quality_options[self.selected_quality])
                    .show_ui(ui, |ui| {
                        for (i, quality) in self.quality_options.iter().enumerate() {
                            ui.selectable_value(&mut self.selected_quality, i, quality);
                        }
                    });
                
                ui.checkbox(&mut self.download_subtitles, "Download Subtitles");
                ui.checkbox(&mut self.extract_thumbnail, "Extract Thumbnail");
            });
            
            ui.separator();
            
            ui.vertical(|ui| {
                ui.checkbox(&mut self.playlist_mode, "Playlist Mode");
                ui.checkbox(&mut self.resume_downloads, "Resume Downloads");
            });
        });
    }
    
    fn draw_advanced_options(&mut self, ui: &mut egui::Ui) {
        if !self.show_advanced {
            return;
        }
        
        ui.separator();
        ui.group(|ui| {
            egui::ScrollArea::vertical()
                .max_height(300.0)
                .show(ui, |ui| {
                    ui.collapsing("Network Options", |ui| {
                        ui.checkbox(&mut self.enable_speed_limit, "Limit Speed");
                        if self.enable_speed_limit {
                            ui.horizontal(|ui| {
                                ui.label("Speed Limit:");
                                ui.text_edit_singleline(&mut self.speed_limit);
                                ui.label("(e.g., 1M, 500K)");
                            });
                        }
                        
                        ui.checkbox(&mut self.use_proxy, "Use Proxy");
                        if self.use_proxy {
                            ui.horizontal(|ui| {
                                ui.label("Proxy URL:");
                                ui.text_edit_singleline(&mut self.proxy_url);
                            });
                        }
                        
                        ui.horizontal(|ui| {
                            ui.label("Max Retries:");
                            ui.add(egui::Slider::new(&mut self.max_retries, 0..=10));
                        });
                    });
                    
                    ui.collapsing("Chunk Downloading", |ui| {
                        ui.checkbox(&mut self.chunk_settings.enabled, "Enable Chunked Downloading");
                        
                        if self.chunk_settings.enabled {
                            ui.horizontal(|ui| {
                                ui.label("Number of Chunks:");
                                ui.add(egui::Slider::new(&mut self.chunk_settings.num_chunks, 2..=16));
                            });
                            
                            ui.horizontal(|ui| {
                                ui.label("Max Parallel:");
                                ui.add(egui::Slider::new(&mut self.chunk_settings.max_parallel_chunks, 1..=8));
                            });
                            
                            ui.checkbox(&mut self.chunk_settings.auto_merge, "Auto-merge Chunks");
                            ui.checkbox(&mut self.chunk_settings.verify_chunks, "Verify Chunks");
                        }
                    });
                    
                    ui.collapsing("Authentication", |ui| {
                        ui.checkbox(&mut self.use_auth, "Use Authentication");
                        if self.use_auth {
                            ui.horizontal(|ui| {
                                ui.label("Username:");
                                ui.text_edit_singleline(&mut self.username);
                            });
                            ui.horizontal(|ui| {
                                ui.label("Password:");
                                ui.add(egui::TextEdit::singleline(&mut self.password).password(true));
                            });
                        }
                        
                        ui.checkbox(&mut self.use_cookies, "Use Cookies File");
                        if self.use_cookies {
                            ui.horizontal(|ui| {
                                ui.label("Cookies File:");
                                ui.text_edit_singleline(&mut self.cookies_file);
                            });
                        }
                    });
                    
                    ui.collapsing("Video Editing", |ui| {
                        ui.checkbox(&mut self.enable_trimming, "Trim Video");
                        if self.enable_trimming {
                            ui.horizontal(|ui| {
                                ui.label("Start Time (HH:MM:SS):");
                                ui.text_edit_singleline(&mut self.start_time);
                            });
                            ui.horizontal(|ui| {
                                ui.label("End Time (HH:MM:SS):");
                                ui.text_edit_singleline(&mut self.end_time);
                            });
                        }
                    });
                    
                    ui.collapsing("Metadata & Files", |ui| {
                        ui.checkbox(&mut self.embed_metadata, "Embed Metadata");
                        ui.checkbox(&mut self.embed_thumbnail, "Embed Thumbnail");
                        ui.checkbox(&mut self.download_description, "Download Description");
                        
                        ui.checkbox(&mut self.use_custom_filename, "Custom Filename Template");
                        if self.use_custom_filename {
                            ui.horizontal(|ui| {
                                ui.label("Template:");
                                ui.text_edit_singleline(&mut self.filename_template);
                            });
                        }
                    });
                });
        });
    }
    
    fn draw_queue_and_history(&mut self, ui: &mut egui::Ui) {
        ui.horizontal(|ui| {
            if ui.button(format!("Queue ({})", self.download_queue.len())).clicked() {
                // Toggle queue view
            }
            
            if ui.button(if self.show_history { "Hide History" } else { "Show History" }).clicked() {
                self.show_history = !self.show_history;
            }
            
            if ui.button("Clear History").clicked() {
                self.download_history.clear();
            }
        });
        
        if !self.download_queue.is_empty() {
            ui.group(|ui| {
                ui.label("Download Queue:");
                egui::ScrollArea::vertical()
                    .max_height(100.0)
                    .show(ui, |ui| {
                        let mut to_remove = Vec::new();
                        for (i, item) in self.download_queue.iter().enumerate() {
                            ui.horizontal(|ui| {
                                ui.label(format!("{}. ", i + 1));
                                ui.label(&item.url);
                                if ui.small_button("Remove").clicked() {
                                    to_remove.push(i);
                                }
                            });
                        }
                        
                        // Remove in reverse order to avoid index shifting
                        for &i in to_remove.iter().rev() {
                            self.download_queue.remove(i);
                        }
                    });
            });
        }
        
        if self.show_history && !self.download_history.is_empty() {
            ui.group(|ui| {
                ui.label("Download History:");
                egui::ScrollArea::vertical()
                    .max_height(150.0)
                    .show(ui, |ui| {
                        for entry in &self.download_history {
                            ui.horizontal(|ui| {
                                ui.label(&entry.timestamp);
                                ui.separator();
                                ui.label(&entry.status);
                                ui.separator();
                                ui.label(&entry.url);
                            });
                        }
                    });
            });
        }
    }
    
    fn start_web_ui(&mut self) {
        if self.web_ui.enabled && !*self.web_ui.running.lock().unwrap() {
            let running = self.web_ui.running.clone();
            let shutdown_tx = {
                let (tx, rx) = std::sync::mpsc::channel();
                let tx_clone = tx.clone();
                
                std::thread::spawn(move || {
                    let rt = Runtime::new().unwrap();
                    rt.block_on(async move {
                        let make_service = service_fn(move |req| {
                            let running = running.clone();
                            let tx = tx_clone.clone();
                            async move {
                                handle_web_request(req, running, tx).await
                            }
                        });
                        
                        let server = Server::bind(&([127, 0, 0, 1], self.web_ui.port).into())
                            .serve(make_service);
                        if let Err(e) = server.await {
                            eprintln!("Web server error: {}", e);
                        }
                    });
                });
                Some(tx)
            };
            
            *self.web_ui.shutdown_tx = shutdown_tx;
            *running.lock().unwrap() = true;
            *self.status.lock().unwrap() = format!("Web UI running at http://localhost:{}", self.web_ui.port);
        }
    }
    
    fn stop_web_ui(&mut self) {
        if let Some(tx) = self.web_ui.shutdown_tx.take() {
            let _ = tx.send(());
            *self.web_ui.running.lock().unwrap() = false;
            *self.status.lock().unwrap() = "Web UI stopped".to_string();
        }
    }
}

async fn handle_web_request(
    req: Request<Incoming>,
    running: Arc<Mutex<bool>>,
    shutdown_tx: std::sync::mpsc::Sender<()>
) -> Result<Response<Bytes>, hyper::Error> {
    match (req.method(), req.uri().path()) {
        (Method::GET, "/") => {
            let html = r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Downloader Web UI</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .btn-danger { background-color: #dc3545; }
        .btn-danger:hover { background-color: #c82333; }
        .status { margin-top: 15px; padding: 10px; background-color: #f8f9fa; border-left: 4px solid #007bff; }
        .progress-bar { height: 20px; background-color: #e9ecef; border-radius: 10px; margin: 10px 0; overflow: hidden; }
        .progress { height: 100%; background-color: #007bff; transition: width 0.3s ease; }
        .log { max-height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-top: 15px; font-family: monospace; }
        .controls { display: flex; gap: 10px; margin-top: 20px; }
        .section { margin-bottom: 25px; padding: 15px; border: 1px solid #eee; border-radius: 6px; }
        .toggle-switch { position: relative; display: inline-block; width: 50px; height: 24px; }
        .toggle-switch input { opacity: 0; width: 0; height: 0; }
        .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #ccc; transition: .4s; border-radius: 34px; }
        .slider:before { position: absolute; content: ""; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; }
        .toggle-switch input:checked + .slider { background-color: #007bff; }
        .toggle-switch input:checked + .slider:before { transform: translateX(26px); }
    </style>
</head>
<body>
    <div class="container">
        <h1>YouTube Downloader Web UI</h1>
        
        <div class="section">
            <h2>Download Settings</h2>
            <div class="form-group">
                <label for="url">YouTube URL:</label>
                <input type="text" id="url" placeholder="https://www.youtube.com/watch?v=...">
            </div>
            
            <div class="form-group">
                <label for="format">Format:</label>
                <select id="format">
                    <option value="best">Best Quality</option>
                    <option value="bestvideo+bestaudio">Best Video + Audio</option>
                    <option value="mp4">MP4</option>
                    <option value="webm">WebM</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="audio-only">Audio Only:</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="audio-only">
                    <span class="slider"></span>
                </label>
            </div>
            
            <div class="form-group">
                <label for="output-path">Output Path:</label>
                <input type="text" id="output-path" value="./downloads/" disabled>
            </div>
            
            <div class="form-group">
                <button id="download-btn">Download</button>
                <button id="cancel-btn" class="btn-danger">Cancel</button>
            </div>
        </div>
        
        <div class="section">
            <h2>Status</h2>
            <div id="status" class="status">Ready</div>
            
            <div class="progress-bar">
                <div id="progress" class="progress" style="width: 0%"></div>
            </div>
            
            <div id="details" style="margin: 10px 0;">
                <div>Speed: --</div>
                <div>ETA: --</div>
            </div>
        </div>
        
        <div class="section">
            <h2>Download Log</h2>
            <div id="log" class="log"></div>
        </div>
        
        <div class="controls">
            <button id="refresh-btn">Refresh Status</button>
            <button id="stop-webui-btn" class="btn-danger">Stop Web UI</button>
        </div>
    </div>

    <script>
        const urlInput = document.getElementById('url');
        const formatSelect = document.getElementById('format');
        const audioOnlyCheckbox = document.getElementById('audio-only');
        const downloadBtn = document.getElementById('download-btn');
        const cancelBtn = document.getElementById('cancel-btn');
        const statusDiv = document.getElementById('status');
        const progressDiv = document.getElementById('progress');
        const detailsDiv = document.getElementById('details');
        const logDiv = document.getElementById('log');
        const refreshBtn = document.getElementById('refresh-btn');
        const stopWebUiBtn = document.getElementById('stop-webui-btn');
        
        function updateLog(message) {
            const p = document.createElement('p');
            p.textContent = new Date().toLocaleTimeString() + ': ' + message;
            logDiv.appendChild(p);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(status, progress, speed, eta) {
            statusDiv.textContent = status;
            progressDiv.style.width = progress + '%';
            detailsDiv.innerHTML = '<div>Speed: ' + speed + '</div><div>ETA: ' + eta + '</div>';
        }
        
        downloadBtn.addEventListener('click', () => {
            if (!urlInput.value.trim()) {
                updateLog('Please enter a YouTube URL');
                return;
            }
            
            updateLog('Starting download...');
            fetch('/api/download', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    url: urlInput.value,
                    format: formatSelect.value,
                    audio_only: audioOnlyCheckbox.checked
                })
            })
            .then(response => response.json())
            .then(data => {
                updateLog(data.message);
            })
            .catch(error => {
                updateLog('Error: ' + error.message);
            });
        });
        
        cancelBtn.addEventListener('click', () => {
            updateLog('Cancelling download...');
            fetch('/api/cancel', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                updateLog(data.message);
            })
            .catch(error => {
                updateLog('Error: ' + error.message);
            });
        });
        
        refreshBtn.addEventListener('click', () => {
            fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                updateStatus(data.status, data.progress, data.speed, data.eta);
            })
            .catch(error => {
                updateLog('Error fetching status: ' + error.message);
            });
        });
        
        stopWebUiBtn.addEventListener('click', () => {
            if (confirm('Are you sure you want to stop the Web UI?')) {
                fetch('/api/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    updateLog(data.message);
                    alert('Web UI stopped');
                })
                .catch(error => {
                    updateLog('Error stopping Web UI: ' + error.message);
                });
            }
        });
        
        // Poll for updates every 2 seconds
        setInterval(() => {
            fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                updateStatus(data.status, data.progress, data.speed, data.eta);
            })
            .catch(error => {
                console.error('Error polling status:', error);
            });
        }, 2000);
        
        // Auto-scroll log
        logDiv.addEventListener('DOMNodeInserted', () => {
            logDiv.scrollTop = logDiv.scrollHeight;
        });
    </script>
</body>
</html>
"#;
            let body = Bytes::from(html);
            Ok(Response::builder()
                .status(StatusCode::OK)
                .header(CONTENT_TYPE, "text/html; charset=utf-8")
                .body(body))
        },
        
        (Method::POST, "/api/download") => {
            let bytes = to_bytes(req.into_body()).await?;
            let json: serde_json::Value = serde_json::from_slice(&bytes).unwrap();
            
            let url = json.get("url").and_then(|v| v.as_str()).unwrap_or("");
            let format = json.get("format").and_then(|v| v.as_str()).unwrap_or("best");
            let audio_only = json.get("audio_only").and_then(|v| v.as_bool()).unwrap_or(false);
            
            let response = serde_json::json!({
                "success": true,
                "message": format!("Download started for: {}", url)
            });
            
            let json_str = serde_json::to_string(&response)?;
            let body = Bytes::from(json_str.as_bytes());
            Ok(Response::builder()
                .status(StatusCode::OK)
                .header(CONTENT_TYPE, "application/json")
                .body(body))
        },
        
        (Method::POST, "/api/cancel") => {
            let response = serde_json::json!({
                "success": true,
                "message": "Download cancellation requested"
            });
            
            let json_str = serde_json::to_string(&response)?;
            let body = Bytes::from(json_str.as_bytes());
            Ok(Response::builder()
                .status(StatusCode::OK)
                .header(CONTENT_TYPE, "application/json")
                .body(body))
        },
        
        (Method::GET, "/api/status") => {
            let response = serde_json::json!({
                "status": "Ready",
                "progress": 0.0,
                "speed": "0 MB/s",
                "eta": "--:--"
            });
            
            let json_str = serde_json::to_string(&response)?;
            let body = Bytes::from(json_str.as_bytes());
            Ok(Response::builder()
                .status(StatusCode::OK)
                .header(CONTENT_TYPE, "application/json")
                .body(body))
        },
        
        (Method::POST, "/api/stop") => {
            let _ = shutdown_tx.send(());
            let response = serde_json::json!({
                "success": true,
                "message": "Web UI stopping..."
            });
            
            let json_str = serde_json::to_string(&response)?;
            let body = Bytes::from(json_str.as_bytes());
            Ok(Response::builder()
                .status(StatusCode::OK)
                .header(CONTENT_TYPE, "application/json")
                .body(body))
        },
        
        _ => {
            Ok(Response::builder()
                .status(StatusCode::NOT_FOUND)
                .body(Bytes::from("Not found")))
        }
    }
}

impl eframe::App for YouTubeDownloader {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        self.apply_theme(ctx);
        self.process_download_queue();
        
        // Start web UI if enabled
        self.start_web_ui();
        
        egui::CentralPanel::default().show(ctx, |ui| {
            // Header
            self.draw_header(ui);
            ui.add_space(10.0);
            
            // Main controls
            self.draw_main_controls(ui);
            ui.add_space(10.0);
            
            // Basic options
            ui.group(|ui| {
                ui.label("Options:");
                self.draw_basic_options(ui);
            });
            ui.add_space(10.0);
            
            // Advanced options (collapsible)
            self.draw_advanced_options(ui);
            ui.add_space(10.0);
            
            // Progress section
            self.draw_progress(ui);
            ui.add_space(10.0);
            
            // Status section
            self.draw_status(ui);
            ui.add_space(10.0);
            
            // Queue and history
            self.draw_queue_and_history(ui);
            
            // Web UI toggle
            ui.horizontal(|ui| {
                ui.checkbox(&mut self.web_ui.enabled, "Enable Web UI");
                if self.web_ui.enabled {
                    ui.label(format!("Port: {}", self.web_ui.port));
                    ui.text_edit_singleline(&mut self.web_ui.port.to_string());
                    if let Some(port_str) = self.web_ui.port.to_string().parse::<u16>().ok() {
                        self.web_ui.port = port_str;
                    }
                }
            });
            
            // Web UI control buttons
            ui.horizontal(|ui| {
                if ui.button("Start Web UI").clicked() {
                    self.start_web_ui();
                }
                
                if ui.button("Stop Web UI").clicked() {
                    self.stop_web_ui();
                }
            });
        });
        
        // Request repaint for progress updates
        if *self.is_downloading.lock().unwrap() {
            ctx.request_repaint_after(Duration::from_millis(100));
        }
    }
}

fn main() -> Result<(), eframe::Error> {
    // Check if yt-dlp is available
    match Command::new("yt-dlp").arg("--version").output() {
        Ok(output) => {
            if !output.status.success() {
                println!("Warning: yt-dlp not found. Please install it from https://github.com/yt-dlp/yt-dlp");
            }
        }
        Err(_) => {
            println!("Warning: yt-dlp not found. Please install it from https://github.com/yt-dlp/yt-dlp");
        }
    }
    
    let options = NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([1000.0, 700.0])
            .with_min_inner_size([800.0, 600.0])
            .with_title("YouTube Downloader"),
        ..Default::default()
    };
    
    eframe::run_native(
        "YouTube Downloader",
        options,
        Box::new(|_cc| {
            Ok(Box::new(YouTubeDownloader::default()))
        }),
    )
}